import 'package:Kairos/core/di/injection_container.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/features/schools/data/datasources/schools_local_datasource.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'dart:convert';
import 'dart:typed_data';

import 'package:Kairos/features/profile/presentation/pages/profile/profile.page.dart';

class HeroWidget extends StatefulWidget {
  const HeroWidget({
    super.key,
    this.pageSection = HeaderEnum.dashboard,
    this.userName,
  });

  final HeaderEnum pageSection;
  final String? userName;

  @override
  State<HeroWidget> createState() => _HeroWidgetState();
}

class _HeroWidgetState extends State<HeroWidget> {
  final String? fullName = sl<AuthLocalDataSource>().getFullName();
  final SchoolsLocalDataSource _schoolsLocalDataSource = sl<SchoolsLocalDataSource>();

  Uint8List? _profileImageBytes;
  bool _isLoadingImage = true;
  bool _hasImageError = false;

  @override
  void initState() {
    super.initState();
    _loadUserProfileImage();
  }

  /// Load user profile image from SharedPreferences
  Future<void> _loadUserProfileImage() async {
    try {
      // First try to get the user photo directly from SharedPreferences
      debugPrint("HeroWidget: Loading user profile image");
      final String? userPhotoBase64 = _schoolsLocalDataSource.getUserPhotoBase64();
      debugPrint("HeroWidget: User profile image loaded from SharedPreferences: $userPhotoBase64");

      if (userPhotoBase64 != null && userPhotoBase64.isNotEmpty) {
        final imageBytes = base64Decode(userPhotoBase64);
        if (mounted) {
          setState(() {
            _profileImageBytes = imageBytes;
            _isLoadingImage = false;
            _hasImageError = false;
          });
        }
        return;
      }

      // If no direct photo, try to get from user profile
      final userProfile = await _schoolsLocalDataSource.getUserProfile();
      if (userProfile != null && userProfile.photo.isNotEmpty) {
        final imageBytes = base64Decode(userProfile.photo);
        if (mounted) {
          setState(() {
            _profileImageBytes = imageBytes;
            _isLoadingImage = false;
            _hasImageError = false;
          });
        }
        return;
      }

      // No profile image found, use default
      if (mounted) {
        setState(() {
          _profileImageBytes = null;
          _isLoadingImage = false;
          _hasImageError = false;
        });
      }
    } catch (e) {
      // Error loading image, use default
      debugPrint('Error loading profile image: $e');
      if (mounted) {
        setState(() {
          _profileImageBytes = null;
          _isLoadingImage = false;
          _hasImageError = true;
        });
      }
    }
  }

  /// Build the profile avatar widget
  Widget _buildProfileAvatar() {
    if (_isLoadingImage) {
      return CircleAvatar(
        radius: 20,
        backgroundColor: Colors.grey[300],
        child: const SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      );
    }

    if (_profileImageBytes != null && !_hasImageError) {
      return CircleAvatar(
        radius: 20,
        backgroundImage: MemoryImage(_profileImageBytes!),
        onBackgroundImageError: (exception, stackTrace) {
          debugPrint('Error displaying profile image: $exception');
          if (mounted) {
            setState(() {
              _hasImageError = true;
            });
          }
        },
      );
    }

    // Default fallback image
    return CircleAvatar(
      radius: 20,
      backgroundImage: const AssetImage("assets/images/default_profile_image.jpg"),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: "hero_dashboard",
      transitionOnUserGestures: true,
      child: Stack(
        children: [
          Image.asset(
            widget.pageSection == HeaderEnum.dashboard ? "assets/images/header_dashboard.png" :
            widget.pageSection == HeaderEnum.notes ? "assets/images/header_notes.png" :
            widget.pageSection == HeaderEnum.absences ? "assets/images/header_absences.png" :
            widget.pageSection == HeaderEnum.cahierDeTexte ? "assets/images/header_cahier_texte.png" :
            widget.pageSection == HeaderEnum.planning ? "assets/images/header_planning.png" :
            widget.pageSection == HeaderEnum.dossiers ? "assets/images/header_dossiers.png" :
            widget.pageSection == HeaderEnum.finances ? "assets/images/header_finances.png" :
            "assets/images/header_dashboard.png",
            height: 189,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
          ),
          Positioned(
            top: 60,
            left: 20,
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ProfilePage(),
                  ),
                );
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SvgPicture.asset(
                    widget.pageSection == HeaderEnum.dashboard
                        ? "assets/images/logo_kairos.svg"
                        : "assets/images/logo_kairos_blanc.svg"
                  ),
                  SizedBox(height: 10),
                  Text(
                    widget.userName ?? fullName ?? "",
                    textAlign: TextAlign.left,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  _buildProfileAvatar(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}