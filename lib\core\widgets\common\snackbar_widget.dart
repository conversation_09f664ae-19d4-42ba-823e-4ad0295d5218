

import 'package:flutter/material.dart';

class CustomSnackbar { 
  
  const CustomSnackbar({required this.message});
  final String message;

  SnackBar getSnackBar(){
    return SnackBar(
      content: Text(message, textAlign: TextAlign.center),
      width: 300,
      duration: const Duration(seconds: 4),
      shape: const OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(20.0))),
      behavior: SnackBarBehavior.floating,
    );
  }
}