import 'package:flutter/material.dart';

class DateFilterDialog extends StatefulWidget {
  final bool showYear;
  const DateFilterDialog({super.key, this.showYear = false});

  @override
  State<DateFilterDialog> createState() => _DateFilterDialogState();
}

class _DateFilterDialogState extends State<DateFilterDialog> {
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();

  Future<void> _selectDate(BuildContext context, TextEditingController controller) async {
    // Check if the widget is still mounted before calling async functions
    if (!mounted) return;
    debugPrint("showYear: ${widget.showYear}");
    final dynamic picked = widget.showYear
        ? await showDialog<int>(
            // ignore: use_build_context_synchronously
            context: context,
            builder: (dialogContext) {
              return Dialog(
                child: SizedBox(
                  width: 300,
                  height: 200,
                  child: YearPicker(
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2030),
                    selectedDate: DateTime.now(),
                    onChanged: (DateTime dateTime) {
                      Navigator.pop(dialogContext, dateTime.year);
                    },
                  ),
                ),
              );
            },
          )
        : await showDatePicker(
          // ignore: use_build_context_synchronously
            context: context,
            cancelText: "Annuler",
            confirmText: "OK",
            helpText: "Sélectionner une date",
            initialDate: DateTime.now(),
            firstDate: DateTime(2000),
            lastDate: DateTime(2101),
          );
    if(!mounted) return;
    if (picked != null) {
      setState(() {
        controller.text = widget.showYear ? picked.toString() : "${picked.toLocal()}".split(' ')[0];
      });
    }
  }

  @override
  void dispose() {
    _startDateController.dispose();
    _endDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                 Text(
                  "SÉLECTIONNER UNE ${widget.showYear? 'ANNÉE':'PÉRIODE'}",
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  textAlign: TextAlign.center,
                ),
                 Divider(
                  color: Theme.of(context).primaryColor,
                  indent: 50,
                  endIndent: 50,
                  height: 32,
                  thickness: 4,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if(!widget.showYear) TextField(
                      controller: _startDateController,
                      readOnly: true,
                      onTap: () => _selectDate(context, _startDateController),
                      decoration: InputDecoration(
                        hintText: "Du",
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      ),
                    ),
                   if(!widget.showYear)  const SizedBox(height: 12),
                    TextField(
                      controller: _endDateController,
                      readOnly: true,
                      onTap: () => _selectDate(context, _endDateController),
                      decoration: InputDecoration(
                        hintText: widget.showYear? "Année" : "Au",
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: FilledButton(
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                      minimumSize: WidgetStateProperty.all(const Size(0, 48)),
                      shape: WidgetStateProperty.all(
                        RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
                      ),
                    ),
                    onPressed: () {
                      // Validate that both dates are selected before closing
                      final String startDate = _startDateController.text.trim();
                      final String endDate = _endDateController.text.trim();

                      if (!widget.showYear && startDate.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Veuillez sélectionner la date de début'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      if (endDate.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(widget.showYear
                              ? 'Veuillez sélectionner une année'
                              : 'Veuillez sélectionner la date de fin'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      debugPrint("Searching from $startDate to $endDate");
                      final result = {
                        "startDate": startDate,
                        "endDate": endDate,
                      };
                      Navigator.of(context).pop(result); // Close dialog
                    },
                    child: const Text(
                      "RECHERCHER",
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: Material(
              color: Theme.of(context).colorScheme.secondary,
              shape: const CircleBorder(),
              child: InkWell(
                borderRadius: BorderRadius.circular(14),
                onTap: () => Navigator.of(context).pop(),
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Icon(Icons.close, color: Colors.white, size: 20),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}