﻿import 'package:flutter_bloc/flutter_bloc.dart';
import 'absences_state.dart';

/// Absences Cubit for managing absences state
class AbsencesCubit extends Cubit<AbsencesState> {
  // TODO: Inject absences use cases
  
  AbsencesCubit() : super(const AbsencesInitial());
  
  /// Load absences data
  Future<void> loadAbsencesData() async {
    emit(const AbsencesLoading());
    
    try {
      // TODO: Implement load absences use case
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      // TODO: Replace with actual data
      emit(const AbsencesLoaded(data: []));
    } catch (e) {
      emit(AbsencesError(e.toString()));
    }
  }
  
  /// Refresh absences data
  Future<void> refresh() async {
    await loadAbsencesData();
  }
}
