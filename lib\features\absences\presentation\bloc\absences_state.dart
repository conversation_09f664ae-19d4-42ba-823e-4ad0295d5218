﻿import 'package:equatable/equatable.dart';

/// Base absences state
abstract class AbsencesState extends Equatable {
  const AbsencesState();
  
  @override
  List<Object?> get props => [];
}

/// Initial absences state
class AbsencesInitial extends AbsencesState {
  const AbsencesInitial();
}

/// Loading state during absences operations
class AbsencesLoading extends AbsencesState {
  const AbsencesLoading();
}

/// Absences data loaded successfully
class AbsencesLoaded extends AbsencesState {
  final List<dynamic> data; // TODO: Replace with proper type
  
  const AbsencesLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// Absences error occurred
class AbsencesError extends AbsencesState {
  final String message;
  
  const AbsencesError(this.message);
  
  @override
  List<Object?> get props => [message];
}
