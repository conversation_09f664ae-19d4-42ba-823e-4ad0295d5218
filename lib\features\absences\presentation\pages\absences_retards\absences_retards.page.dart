import 'package:flutter/material.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:Kairos/features/absences/data/models/absence.dart';
import 'package:Kairos/features/absences/data/models/retard.dart';
import 'package:Kairos/features/absences/presentation/pages/absences_retards/absence_item.widget.dart';
import 'package:Kairos/core/utils/date_utils.dart';

class AbsencesRetardsPage extends StatefulWidget{
  const AbsencesRetardsPage({super.key});

  @override
  State<AbsencesRetardsPage> createState() => _AbsencesRetardsPageState();
}

class _AbsencesRetardsPageState extends State<AbsencesRetardsPage> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isSearchBarVisible = false;
  bool _isLoading = true;
  final TextEditingController _searchController = TextEditingController();
  late List<Absence> filteredAbsences = [];
  late List<Retard> filteredTardiness = [];
  late AnimationController _searchAnimationController;

  // Date filter state
  String? _startDateFilter;
  String? _endDateFilter;

  final List<Absence> absencesData = [
    Absence(
      type: 'A',
      date: '23/11/2022',
      course: 'ALT_COURS3_S2_N1',
      teacher: 'AVEC MALICK Y. FALL EN PALT_C1B_2021',
      isJustified: false,
    ),
    Absence(
      type: 'A',
      date: '15/11/2022',
      course: 'ALT_COURS2_S1_N2',
      teacher: 'AVEC MALICK Y. FALL EN PALT_C1B_2021',
      isJustified: true,
    ),
    Absence(
      type: 'A',
      date: '10/11/2022',
      course: 'ALT_COURS1_S1_N3',
      teacher: 'AVEC MALICK Y. FALL EN PALT_C1B_2021',
      isJustified: false,
    ),
  ];

  final List<Retard> tardinessData = [
    Retard(
      type: 'R',
      date: '23/11/2022',
      course: 'ALT_COURS2_S1_N1',
      teacher: 'AVEC MALICK Y. FALL EN PALT_C1B_2021',
      isJustified: true,
      duration: '30mn',
    ),
    Retard(
      type: 'R',
      date: '18/11/2022',
      course: 'ALT_COURS1_S1_N2',
      teacher: 'AVEC MALICK Y. FALL EN PALT_C1B_2021',
      isJustified: false,
      duration: '15mn',
    ),
  ];



  @override
  void initState(){
    super.initState();
    filteredAbsences = List.from(absencesData);
    filteredTardiness = List.from(tardinessData);
    _tabController = TabController(length: 2, vsync: this);
    _searchController.addListener(_filterAbsencesRetards);
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  void dispose(){
    _tabController.dispose();
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }


  void _filterAbsencesRetards(){
    final String query = _searchController.text.toLowerCase();
    final int currentIndex = _tabController.index;
    setState(() {
      if(currentIndex == 0){
        // Absences tab
        filteredTardiness = List.from(tardinessData);
        filteredAbsences = absencesData.where((a) {
          // Text search filter
          bool matchesText = a.course.toLowerCase().contains(query) ||
              a.teacher.toLowerCase().contains(query) ||
              a.date.toLowerCase().contains(query);

          // Date range filter
          bool matchesDateRange = true;
          if (_startDateFilter != null && _endDateFilter != null) {
            matchesDateRange = isDateInRange(a.date, _startDateFilter!, _endDateFilter!);
          }

          return matchesText && matchesDateRange;
        }).toList();
      } else {
        // Retards tab
        filteredAbsences = List.from(absencesData);
        filteredTardiness = tardinessData.where((r) {
          // Text search filter
          bool matchesText = r.course.toLowerCase().contains(query) ||
              r.teacher.toLowerCase().contains(query) ||
              r.date.toLowerCase().contains(query);

          // Date range filter
          bool matchesDateRange = true;
          if (_startDateFilter != null && _endDateFilter != null) {
            matchesDateRange = isDateInRange(r.date, _startDateFilter!, _endDateFilter!);
          }

          return matchesText && matchesDateRange;
        }).toList();
      }
    });
  }

  // Method to handle date filter changes
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    // Apply the filter immediately
    _filterAbsencesRetards();
  }

  // Method to clear date filters
  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    // Reapply filters without date constraint
    _filterAbsencesRetards();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          CustomAppBar(pageSection: HeaderEnum.absences, title: "ABSENCES & RETARDS",
            isSearchBarVisible: _isSearchBarVisible,
            onSearchTap: () {
              setState(() {
                _isSearchBarVisible = !_isSearchBarVisible;
                if (!_isSearchBarVisible) {
                  _searchAnimationController.reverse();
                  _searchController.clear(); // Clear search when hidden
                  _startDateFilter = null; // Clear date filters when hidden
                  _endDateFilter = null;
                  _filterAbsencesRetards(); // Reset filter
                } else {
                  _searchAnimationController.forward();
                }
              });
            },
          ),
          SliverToBoxAdapter(
            child: TabBar(
            controller: _tabController,
            indicatorColor: Colors.red,
            indicatorSize: TabBarIndicatorSize.tab,
            tabs: [
              Tab(text: "ABSENCES"),
              Tab(text: "RETARDS"),
            ],
          ),
          ),
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                delegate: SearchBarSliver(
                  extentHeight: _searchAnimationController.value * 60.0, // Animate height
                  searchController: _searchController,
                  onSearchChanged: (query) => _filterAbsencesRetards(),
                  onDateFilterChanged: _onDateFilterChanged,
                  onClearDateFilter: _clearDateFilter,
                  hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
                  hintText: "Rechercher absence(s) ou retard(s)...",
                ),
              );
            },
          ),
          _isLoading
          ? SliverFillRemaining(
              child: Center(
                child: CustomSpinner(
                  size: 60.0,
                  strokeWidth: 5.0,
                ),
              ),
            )
          : SliverFillRemaining(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Absences tab
                ListView.builder(
                  padding: EdgeInsets.only(top: 16.0, bottom: 16.0),
                  itemCount: filteredAbsences.length,
                  itemBuilder: (context, index) {
                    final absence = filteredAbsences[index];
                    return AbsenceItem(
                      type: absence.type,
                      date: absence.date,
                      course: absence.course,
                      teacher: absence.teacher,
                      isJustified: absence.isJustified,
                    );
                  },
                ),
                // Retards tab
                ListView.builder(
                  padding: EdgeInsets.only(top: 16.0, bottom: 16.0),
                  itemCount: filteredTardiness.length,
                  itemBuilder: (context, index) {
                    final tardiness = filteredTardiness[index];
                    return AbsenceItem(
                      type: tardiness.type,
                      date: tardiness.date,
                      course: tardiness.course,
                      teacher: tardiness.teacher,
                      isJustified: tardiness.isJustified,
                      duration: tardiness.duration,
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
