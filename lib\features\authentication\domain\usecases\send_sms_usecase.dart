import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../repositories/auth_repository.dart';

/// Use case for sending SMS verification code
class SendSmsUseCase {
  final AuthRepository repository;
  
  SendSmsUseCase(this.repository);
  
  /// Execute the send SMS use case
  /// 
  /// [phoneNumber] - The phone number to send SMS to
  /// Returns [Either<Failure, void>] - Success or failure result
  Future<Either<Failure, dynamic>> call(String phoneNumber) async {
    return await repository.sendSms(phoneNumber);
  }
}
