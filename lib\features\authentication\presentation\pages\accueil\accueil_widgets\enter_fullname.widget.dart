
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:Kairos/core/di/injection_container.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';

class EnterFullnameWidget extends StatefulWidget{
  const EnterFullnameWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<EnterFullnameWidget> createState() => _EnterFullnameWidgetState();
}

class _EnterFullnameWidgetState extends State<EnterFullnameWidget>{

  final TextEditingController _fullnameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();
  bool _isLoading = false;

  @override
  void dispose(){
    super.dispose();
    _fullnameController.dispose();
  }

  @override
  Widget build(BuildContext context){
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Text("ENTRER VOTRE NOM COMPLET", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
        SvgPicture.asset("assets/images/logo_kairos.svg"),
        SizedBox(height: 20, 
                 width: 200,
                 child: Divider(color: Theme.of(context).primaryColor, thickness: 5),),
        Spacer(),
        Flexible(flex: 8, child: SvgPicture.asset("assets/images/illustration_profil.svg")),
        // Spacer(flex: 2),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Text("Veuillez saisir votre nom complet.", textAlign: TextAlign.center),
        ),
        Spacer(flex: 3),
        Form(
          key: _formKey,
          child: SizedBox(
            width: 300,
            child: TextFormField(
              controller: _fullnameController,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                hintText: "Nom complet",
                hintStyle: TextStyle(color: Colors.grey.shade400),
                contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                ),
              keyboardType: TextInputType.name,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[^0-9]')),
              ],
              validator: (value){
                if(value!.isEmpty){
                  return "Veuillez saisir votre nom complet";
                } else {
                  return null;
                }
              },
            ),
            
          ),
        ),
        Spacer(),
        FilledButton(
          style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
              fixedSize: WidgetStateProperty.all(Size(300, 50))),
          onPressed: _isLoading ? null : () async {
            debugPrint('the user clicked on `Continue` button');
            if(_formKey.currentState!.validate()){
              setState(() {
                _isLoading = true;
              });

              try {
                // Save full name to SharedPreferences
                await _authLocalDataSource.saveFullName(_fullnameController.text.trim());
                debugPrint('Full name saved: ${_fullnameController.text.trim()}');

                // Navigate to next page
                widget.pageController.nextPage(
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeInOut
                );
              } catch (e) {
                debugPrint('Error saving full name: $e');
                // Show error message to user
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur lors de la sauvegarde du nom complet'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } finally {
                if (mounted) {
                  setState(() {
                    _isLoading = false;
                  });
                }
              }
            }
          },
          child: _isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : const Text("CONTINUER", style: TextStyle(fontWeight: FontWeight.bold)),
          ),
        Spacer(flex: 3),
      ],
    );
  }
}