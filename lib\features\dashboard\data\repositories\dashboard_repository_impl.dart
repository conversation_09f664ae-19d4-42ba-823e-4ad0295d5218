import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/dashboard_entity.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../datasources/dashboard_remote_datasource.dart';

/// Implementation of DashboardRepository
class DashboardRepositoryImpl implements DashboardRepository {
  final DashboardRemoteDataSource remoteDataSource;

  DashboardRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, DashboardEntity>> loadDashboardData({
    required String codeEtab,
    required String telephone,
    required String codeEtudiant,
  }) async {
    try {
      final dashboardModel = await remoteDataSource.loadDashboardData(
        codeEtab: codeEtab,
        telephone: telephone,
        codeEtudiant: codeEtudiant,
      );
      
      return Right(dashboardModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
      // Handle Dio errors (HTTP errors, network issues, etc.)
      return Left(ServerFailure('Failed to load dashboard data: ${e.message}'));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}
