import 'package:Kairos/features/dashboard/data/dashboard_item_type.enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/features/dashboard/presentation/pages/dashboard/dashboard_item.widget.dart';
import 'package:Kairos/core/widgets/common/hero_widget.dart';
import 'package:Kairos/core/constants/dashboard_strings.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_utilisateur.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:Kairos/core/di/injection_container.dart';
import 'package:Kairos/features/dashboard/presentation/bloc/dashboard_cubit.dart';
import 'package:Kairos/features/dashboard/presentation/bloc/dashboard_state.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/features/dashboard/domain/entities/dashboard_entity.dart';


class Dashboard extends StatefulWidget {
  const Dashboard({
    super.key,
    this.userName,
  });

  final String? userName;

  @override
  State<Dashboard> createState() => _DashboardState();
}

class _DashboardState extends State<Dashboard> {
  int _currentIndex = 1;
  EtablissementUtilisateur? _school;
  bool _isLoading = true;

  /// Generate dynamic dashboard items based on DashboardEntity data
  List<Map<String, dynamic>> _generateDashboardItems(DashboardEntity? dashboardData) {
    if (dashboardData == null) {
      // Return static items if no data is available
      return [
        {"title": DashboardStrings.notesTitle, "icon": "icone_notes.svg", "itemType": DashboardItemType.notes},
        {"title": DashboardStrings.absencesTitle, "icon": "icone_absences.svg", "itemType": DashboardItemType.absences},
        {"title": DashboardStrings.dossiersTitle, "icon": "icone_dossier_etudiant.svg", "itemType": DashboardItemType.dossiers},
        {"title": DashboardStrings.financesTitle, "icon": "icone_finance.svg", "itemType": DashboardItemType.finances},
        {"title": DashboardStrings.cahierTitle, "icon": "icone_cahier_de_texte.svg", "itemType": DashboardItemType.cahierTexte},
        {"title": DashboardStrings.planningTitle, "icon": "icone_planning_cours.svg", "itemType": DashboardItemType.planning},
        {"title": DashboardStrings.availableRessources, "icon": "icone_ressources_telechargeable.svg", "itemType": DashboardItemType.ressources},
      ];
    }

    return [
      {
        "title": "${dashboardData.note.nombreNote} notes enregistrées pour ${dashboardData.anneeScolaire}",
        "icon": "icone_notes.svg",
        "itemType": DashboardItemType.notes
      },
      {
        "title": "${dashboardData.assiduite.nombreAbsence} absence(s) et ${dashboardData.assiduite.nombreRetard} retard(s) enregistrés pour ${dashboardData.anneeScolaire}",
        "icon": "icone_absences.svg",
        "itemType": DashboardItemType.absences
      },
      {
        "title": "${dashboardData.dossier.nombreElement} éléments ajoutés dans votre dossier pour ${dashboardData.anneeScolaire}",
        "icon": "icone_dossier_etudiant.svg",
        "itemType": DashboardItemType.dossiers
      },
      {
        "title": "${dashboardData.etatFinancier.montantEncaisse.toStringAsFixed(0)} XOF encaissé sur ${dashboardData.etatFinancier.montantAencaisser.toStringAsFixed(0)} pour ${dashboardData.anneeScolaire}",
        "subtitle": "Total impayé: ${dashboardData.etatFinancier.montantImpaye.toStringAsFixed(0)}",
        "icon": "icone_finance.svg",
        "itemType": DashboardItemType.finances
      },
      {
        "title": "${dashboardData.cahierTexte.nombreEntre} entrées de cahier de texte saisies pour ${dashboardData.anneeScolaire}",
        "subtitle": "Semestre ${dashboardData.cahierTexte.semestre}",
        "icon": "icone_cahier_de_texte.svg",
        "itemType": DashboardItemType.cahierTexte
      },
      {
        "title": "${dashboardData.plannings.nombrePlanningSemaine} plannings pour la semaine dont ${dashboardData.plannings.nombrePlanningJour} pour la journée",
        "icon": "icone_planning_cours.svg",
        "itemType": DashboardItemType.planning
      },
      {
        "title": "${dashboardData.ressourcesPedagogiques.nombreRessource} ressources pédagogiques disponibles",
        "icon": "icone_ressources_telechargeable.svg",
        "itemType": DashboardItemType.ressources
      },
    ];
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDashboardData();
    });
  }

  Future<void> _loadDashboardData() async {
    try {
      // Get school data from route arguments
      final args = ModalRoute.of(context)?.settings.arguments as EtablissementUtilisateur?;

      if (args != null) {
        setState(() {
          _school = args;
        });

        // Get phone number from SharedPreferences
        final authLocalDataSource = sl<AuthLocalDataSource>();
        final phoneNumber = await authLocalDataSource.getPhoneNumber();

        if (phoneNumber != null && mounted) {
          // Load dashboard data via BLoC
          context.read<DashboardCubit>().loadDashboardData(
            codeEtab: _school!.codeEtab,
            telephone: phoneNumber,
            codeEtudiant: _school!.codeUtilisateur,
          );
        } else {
          setState(() {
            _isLoading = false;
          });
          // Handle case where phone number is not available
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("Erreur: Numéro de téléphone non disponible")),
            );
          }
        }
      } else {
        setState(() {
          _isLoading = false;
        });
        // Handle case where school data is not available
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Erreur: Données de l'école non disponibles")),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('Error loading dashboard data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erreur lors du chargement des données")),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: BlocListener<DashboardCubit, DashboardState>(
        listener: (context, state) {
          if (state is DashboardLoaded) {
            setState(() {
              _isLoading = false;
            });
          } else if (state is DashboardError) {
            setState(() {
              _isLoading = false;
            });
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text("Erreur: ${state.message}")),
              );
            }
          }
        },
        child: BlocBuilder<DashboardCubit, DashboardState>(
          builder: (context, state) {
            if (state is DashboardLoading || _isLoading) {
              return Center(
                child: CustomSpinner(
                  size: 60.0,
                  strokeWidth: 5.0,
                ),
              );
            }

            // Get dashboard data from state if available
            DashboardEntity? dashboardData;
            if (state is DashboardLoaded) {
              dashboardData = state.dashboardData;
            }

            // Generate dashboard items based on loaded data
            final dashboardItems = _generateDashboardItems(dashboardData);

            return SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                mainAxisSize: MainAxisSize.min,
                children: [
                  HeroWidget(userName: widget.userName),
                  SizedBox(height: 20),
                  Text(DashboardStrings.dashboardTitle, style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
                  Divider(color: Theme.of(context).primaryColor, thickness: 2, height: 20, indent: 100, endIndent: 100,),
                  ...dashboardItems.map((item) => DashboardItem(
                    title: item["title"]!,
                    subtitle: item["subtitle"],
                    iconName: item["icon"]!,
                    itemType: item["itemType"],
                  )),

                ]),
            );
          },
        ),
      ),
        bottomNavigationBar: BottomNavigationBar(
          onTap: (index){
              setState(() => _currentIndex = index);
            if(index == 0){
              debugPrint("user wants to view his profile");
              Navigator.pushNamed(context, "/profile");
            } else if(index == 2){
              debugPrint("user wants to view his notifications");
              Navigator.pushNamed(context, "/notifications");
            } else {
              debugPrint("user wants to view his dashboard");
              Navigator.pushNamed(context, "/dashboard");
            }
          },
          backgroundColor: Colors.black,
          selectedItemColor: Theme.of(context).primaryColor,
          currentIndex: _currentIndex,
          unselectedItemColor: Colors.white,
          items: [
          BottomNavigationBarItem(icon: Icon(Icons.person), label: ""),
          BottomNavigationBarItem(icon: Icon(Icons.dashboard), label: ""),
          BottomNavigationBarItem(icon: Icon(Icons.notifications), label: ""),
        ]),
        );
        
  }
}