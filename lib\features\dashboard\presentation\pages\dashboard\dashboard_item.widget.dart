import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:Kairos/features/absences/presentation/pages/absences_retards/absences_retards.page.dart';
import 'package:Kairos/features/course_log/presentation/pages/cahier_texte/cahier_texte.page.dart';
import 'package:Kairos/features/student_records/presentation/pages/dossiers_etudiant/dossiers.page.dart';
import 'package:Kairos/features/schedule/presentation/pages/emploi_du_temps/emploi_du_temps.page.dart';
import 'package:Kairos/features/finances/presentation/pages/finances/finances.page.dart';
// import 'package:Kairos/features/schools/presentation/pages/liste_etablissements/liste_etablissement_widgets/alert.widget.dart';
import 'package:Kairos/features/grades/presentation/pages/notes/notes.page.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import 'package:Kairos/core/constants/dashboard_strings.dart';
import 'package:Kairos/features/dashboard/data/dashboard_item_type.enum.dart';

// enum DashboardItemType moved to data/dashboard_item_type.enum.dart

class DashboardItem extends StatelessWidget{
  final dynamic title;
  final dynamic subtitle;
  final dynamic iconName;
  final DashboardItemType? itemType;

  const DashboardItem({
    super.key,
    required this.title,
    this.subtitle,
    required this.iconName,
    this.itemType,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Theme.of(context).scaffoldBackgroundColor,
      shadowColor: Colors.black,
      child: ListTile(
        title: Padding(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: ListTile(
            dense: true,
            title: Text(title, style: TextStyle(fontSize: 14)),
            subtitle: subtitle != null ? Text(subtitle) : null,
          ),
        ),
        leading: SvgPicture.asset("assets/icons/$iconName", width: 20, height: 20,),
        trailing: Icon(Icons.arrow_forward_ios, size: 10,),
        onTap: () {
          if (itemType != null) {
            switch(itemType!) {
              case DashboardItemType.notes:
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => NotesPage()),
                );
                break;
              case DashboardItemType.finances:
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => FinancesPage()),
                );
                break;
              case DashboardItemType.absences:
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => AbsencesRetardsPage()),
                );
                break;
              case DashboardItemType.dossiers:
                Navigator.push(context,
                MaterialPageRoute(builder: (context) => DossiersPage()));
                break;
              case DashboardItemType.cahierTexte:
                 Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => CahierTextePage()),
                );
                break;
              case DashboardItemType.planning:
                 Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => EmploiDuTempsPage()),
                );
                break;
              case DashboardItemType.ressources:
                 ScaffoldMessenger.of(context).showSnackBar(
                CustomSnackbar(message: "Fonctionnalité en cours de développement").getSnackBar()
              );
                break;
            }
          } else {
            // Fallback to old logic for backward compatibility
            switch(title.toString()) {
              case DashboardStrings.notesTitle:
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => NotesPage()),
                );
                break;
              case DashboardStrings.financesTitle:
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => FinancesPage()),
                );
                break;
              case DashboardStrings.absencesTitle:
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => AbsencesRetardsPage()),
                );
                break;
              case DashboardStrings.dossiersTitle:
                Navigator.push(context,
                MaterialPageRoute(builder: (context) => DossiersPage()));
                break;
              case DashboardStrings.cahierTitle:
                 Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => CahierTextePage()),
                );
                break;
              case DashboardStrings.planningTitle:
                 Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => EmploiDuTempsPage()),
                );
                break;
              default :
                 ScaffoldMessenger.of(context).showSnackBar(
                CustomSnackbar(message: "Fonctionnalité en cours de développement").getSnackBar()
              );
                break;
            }
          }
        },
      ),
    );
  }
}
