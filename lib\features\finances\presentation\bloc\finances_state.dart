﻿import 'package:equatable/equatable.dart';

/// Base finances state
abstract class FinancesState extends Equatable {
  const FinancesState();
  
  @override
  List<Object?> get props => [];
}

/// Initial finances state
class FinancesInitial extends FinancesState {
  const FinancesInitial();
}

/// Loading state during finances operations
class FinancesLoading extends FinancesState {
  const FinancesLoading();
}

/// Finances data loaded successfully
class FinancesLoaded extends FinancesState {
  final List<dynamic> data; // TODO: Replace with proper type
  
  const FinancesLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// Finances error occurred
class FinancesError extends FinancesState {
  final String message;
  
  const FinancesError(this.message);
  
  @override
  List<Object?> get props => [message];
}
