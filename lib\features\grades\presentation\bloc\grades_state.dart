﻿import 'package:equatable/equatable.dart';

/// Base grades state
abstract class GradesState extends Equatable {
  const GradesState();
  
  @override
  List<Object?> get props => [];
}

/// Initial grades state
class GradesInitial extends GradesState {
  const GradesInitial();
}

/// Loading state during grades operations
class GradesLoading extends GradesState {
  const GradesLoading();
}

/// Grades data loaded successfully
class GradesLoaded extends GradesState {
  final List<dynamic> data; // TODO: Replace with proper type
  
  const GradesLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// Grades error occurred
class GradesError extends GradesState {
  final String message;
  
  const GradesError(this.message);
  
  @override
  List<Object?> get props => [message];
}
