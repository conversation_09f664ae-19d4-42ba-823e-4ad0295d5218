import 'package:flutter/material.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:Kairos/features/grades/presentation/pages/notes/grade_item.widget.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/inputs/search_bar_sliver.widget.dart';
import 'package:Kairos/core/widgets/common/empty_message.widget.dart';
import 'package:Kairos/core/utils/date_utils.dart';

class NotesPage extends StatefulWidget {
  const NotesPage({super.key});

  @override
  State<NotesPage> createState() => _NotesPageState();
}

class _NotesPageState extends State<NotesPage> with SingleTickerProviderStateMixin { // Add SingleTickerProviderStateMixin for animation
  bool _isLoading = true;
  bool _isSearchBarVisible = false; // State to control search bar visibility
  late TextEditingController _searchController; // Controller for the search bar
  List<Map<String, dynamic>> _filteredGrades = []; // List to hold filtered grades

  // Date filter state
  String? _startDateFilter;
  String? _endDateFilter;


  // animation controller
  late AnimationController _searchAnimationController;

  final List<Map<String, dynamic>> grades = [
    {
      'type': 'DEVOIRS',
      'date': '22/12/2021',
      'course': 'ALT_COURS2_S1_N1',
      'teacher': 'Avec Malick Y. FALL',
      'semester': 'PALT_C1B_2021 | SEMESTRE 1',
      'grade': 13.0,
      'classAvg': 11.8,
    },
    {
      'type': 'EXAMEN',
      'date': '15/01/2022',
      'course': 'MATH_S1_EXAM',
      'teacher': 'Avec Sophie M. DIOP',
      'semester': 'PALT_C1B_2021 | SEMESTRE 1',
      'grade': 15.5,
      'classAvg': 12.3,
    },
    {
      'type': 'DEVOIRS',
      'date': '05/02/2022',
      'course': 'PHYS_S1_N2',
      'teacher': 'Avec Pierre L. NDIAYE',
      'semester': 'PALT_C1B_2021 | SEMESTRE 1',
      'grade': 9.5,
      'classAvg': 10.2,
    },
    {
      'type': 'EXAMEN',
      'date': '20/02/2022',
      'course': 'INFO_S1_EXAM',
      'teacher': 'Avec Jean P. SENE',
      'semester': 'PALT_C1B_2021 | SEMESTRE 1',
      'grade': 17.0,
      'classAvg': 14.1,
    },
    {
      'type': 'DEVOIRS',
      'date': '10/03/2022',
      'course': 'CHIM_S1_N1',
      'teacher': 'Avec Marie C. SARR',
      'semester': 'PALT_C1B_2021 | SEMESTRE 1',
      'grade': 8.5,
      'classAvg': 9.7,
    },
    {
      'type': 'DEVOIRS',
      'date': '25/03/2022',
      'course': 'ANGL_S1_N1',
      'teacher': 'Avec John S. MBAYE',
      'semester': 'PALT_C1B_2021 | SEMESTRE 1',
      'grade': 14.0,
      'classAvg': 12.5,
    },
    {
      'type': 'EXAMEN',
      'date': '15/04/2022',
      'course': 'CHIM_S1_EXAM',
      'teacher': 'Avec Marie C. SARR',
      'semester': 'PALT_C1B_2021 | SEMESTRE 1',
      'grade': 11.5,
      'classAvg': 10.8,
    },
    {
      'type': 'DEVOIRS',
      'date': '05/05/2022',
      'course': 'MATH_S2_N1',
      'teacher': 'Avec Sophie M. DIOP',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 12.5,
      'classAvg': 11.0,
    },
    {
      'type': 'DEVOIRS',
      'date': '20/05/2022',
      'course': 'PHYS_S2_N1',
      'teacher': 'Avec Pierre L. NDIAYE',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 7.5,
      'classAvg': 9.2,
    },
    {
      'type': 'EXAMEN',
      'date': '10/06/2022',
      'course': 'ANGL_S2_EXAM',
      'teacher': 'Avec John S. MBAYE',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 16.0,
      'classAvg': 13.7,
    },
    {
      'type': 'DEVOIRS',
      'date': '25/06/2022',
      'course': 'INFO_S2_N1',
      'teacher': 'Avec Jean P. SENE',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 18.5,
      'classAvg': 15.2,
    },
    {
      'type': 'EXAMEN',
      'date': '15/07/2022',
      'course': 'MATH_S2_EXAM',
      'teacher': 'Avec Sophie M. DIOP',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 13.0,
      'classAvg': 11.5,
    },
    {
      'type': 'DEVOIRS',
      'date': '10/09/2022',
      'course': 'PHYS_S2_N2',
      'teacher': 'Avec Pierre L. NDIAYE',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 9.0,
      'classAvg': 10.0,
    },
    {
      'type': 'EXAMEN',
      'date': '25/09/2022',
      'course': 'PHYS_S2_EXAM',
      'teacher': 'Avec Pierre L. NDIAYE',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 10.5,
      'classAvg': 10.2,
    },
    {
      'type': 'DEVOIRS',
      'date': '10/10/2022',
      'course': 'CHIM_S2_N1',
      'teacher': 'Avec Marie C. SARR',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 12.0,
      'classAvg': 11.3,
    },
    {
      'type': 'EXAMEN',
      'date': '30/10/2022',
      'course': 'INFO_S2_EXAM',
      'teacher': 'Avec Jean P. SENE',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 16.5,
      'classAvg': 14.0,
    },
    {
      'type': 'DEVOIRS',
      'date': '15/11/2022',
      'course': 'ANGL_S2_N2',
      'teacher': 'Avec John S. MBAYE',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 13.5,
      'classAvg': 12.0,
    },
    {
      'type': 'EXAMEN',
      'date': '05/12/2022',
      'course': 'CHIM_S2_EXAM',
      'teacher': 'Avec Marie C. SARR',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 8.0,
      'classAvg': 9.5,
    },
    {
      'type': 'DEVOIRS',
      'date': '20/12/2022',
      'course': 'ALT_COURS3_S2_N1',
      'teacher': 'Avec Malick Y. FALL',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 14.5,
      'classAvg': 12.8,
    },
    {
      'type': 'EXAMEN',
      'date': '15/01/2023',
      'course': 'ALT_COURS3_S2_EXAM',
      'teacher': 'Avec Malick Y. FALL',
      'semester': 'PALT_C1B_2021 | SEMESTRE 2',
      'grade': 15.0,
      'classAvg': 13.2,
    },
  ];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(); // Initialize the controller
    _filteredGrades = List.from(grades); // Initialize filtered list with all grades

    // Add listener to filter grades when search text changes
    _searchController.addListener(_filterGrades);
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose(); // Dispose the controller
    _searchAnimationController.dispose();
    super.dispose();
  }

  // Method to filter grades based on search query and date range
  void _filterGrades(){
    final String query = _searchController.text.toLowerCase();
    setState(() {
      _filteredGrades = grades.where((grade) {
        // Text search filter
        bool matchesText = grade['type'].toLowerCase().contains(query) ||
               grade['course'].toLowerCase().contains(query) ||
               grade['teacher'].toLowerCase().contains(query) ||
               grade['date'].toLowerCase().contains(query) ||
               grade['grade'].toString().contains(query) ||
               grade['semester'].toLowerCase().contains(query);

        // Date range filter
        bool matchesDateRange = true;
        if (_startDateFilter != null && _endDateFilter != null) {
          matchesDateRange = isDateInRange(grade['date'], _startDateFilter!, _endDateFilter!);
        }

        return matchesText && matchesDateRange;
      }).toList();
    });
  }

  // Method to handle date filter changes
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    // Apply the filter immediately
    _filterGrades();
  }

  // Method to clear date filters
  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    // Reapply filters without date constraint
    _filterGrades();
  }

  // Method to toggle search bar visibility
  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (!_isSearchBarVisible) {
        _searchAnimationController.reverse();
        _searchController.clear(); // Clear search when hidden
        _startDateFilter = null; // Clear date filters when hidden
        _endDateFilter = null;
        _filterGrades(); 
      } else {
        _searchAnimationController.forward();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: false,
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
            isSearchBarVisible: _isSearchBarVisible,
            pageSection: HeaderEnum.notes,
            title: "ÉVALUATIONS & NOTES",
            onSearchTap: _toggleSearchBarVisibility, // Pass the toggle method
          ),
          // Add the SearchBarSliver here, controlled by _isSearchBarVisible
          AnimatedBuilder(animation: _searchAnimationController,
          builder: (context, child){
            return SliverPersistentHeader( // Wrap the delegate in SliverPersistentHeader
            delegate: SearchBarSliver(
              extentHeight: _searchAnimationController.value * 60.0, // Set minExtent to fixed height when visible
              searchController: _searchController,
              onSearchChanged: (query) => _filterGrades(), // Trigger filter on change
              onDateFilterChanged: _onDateFilterChanged,
              onClearDateFilter: _clearDateFilter,
              hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
              hintText: "Rechercher notes et évaluations...",
            ),
            pinned: true, // Keep the search bar visible when scrolling up
          );
          }),
          _isLoading
          ? SliverFillRemaining(
              child: Center(
                child: CustomSpinner(
                  size: 60.0,
                  strokeWidth: 5.0,
                ),
              ),
            )
          : (_filteredGrades.isNotEmpty? SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final grade = _filteredGrades[index]; // Use filtered list
                  return GradeItem(
                    type: grade['type'],
                    date: grade['date'],
                    course: grade['course'],
                    teacher: grade['teacher'],
                    semester: grade['semester'],
                    grade: grade['grade'],
                    classAvg: grade['classAvg'],
                  );
                },
                childCount: _filteredGrades.length, // Use filtered list count
              ),
            )
          : SliverFillRemaining(
              child: Center(
                child: EmptyMessage(message: "Aucune note trouvée"),
              ),
            )
          ),
        ]
      )
    );
  }
}