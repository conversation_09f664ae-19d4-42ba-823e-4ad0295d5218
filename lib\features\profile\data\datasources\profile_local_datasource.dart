import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../schools/data/models/user_profile_model.dart';

/// Abstract interface for profile local data source
abstract class ProfileLocalDataSource {
  /// Get user profile from SharedPreferences
  Future<UserProfileModel?> getUserProfile();
  
  /// Remove user profile from SharedPreferences
  Future<void> removeUserProfile();

  /// Get phone number from SharedPreferences
  String? getPhoneNumber();
}

/// Implementation of ProfileLocalDataSource
class ProfileLocalDataSourceImpl implements ProfileLocalDataSource {
  final SharedPreferences sharedPreferences;
  
  // Storage key
  static const String _userProfileKey = 'user_profile';

  ProfileLocalDataSourceImpl({required this.sharedPreferences});

  @override
  Future<UserProfileModel?> getUserProfile() async {
    final userProfileJson = sharedPreferences.getString(_userProfileKey);
    if (userProfileJson != null) {
      final userProfileMap = jsonDecode(userProfileJson) as Map<String, dynamic>;
      return UserProfileModel.fromJson(userProfileMap);
    }
    return null;
  }

  @override
  Future<void> removeUserProfile() async {
    await sharedPreferences.remove(_userProfileKey);
  }

   @override
  String? getPhoneNumber() {
    if(sharedPreferences.containsKey('numeroTelephone')){
      return sharedPreferences.getString('numeroTelephone');
    } else {
      return "";
    }
  }
}
