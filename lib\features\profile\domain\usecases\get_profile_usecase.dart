import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/profile_entity.dart';
import '../repositories/profile_repository.dart';

/// Use case for getting user profile from local storage
class GetProfileUseCase implements UseCase<ProfileEntity?, NoParams> {
  final ProfileRepository repository;

  GetProfileUseCase(this.repository);

  @override
  Future<Either<Failure, ProfileEntity?>> call(NoParams params) async {
    return await repository.getProfile();
  }
}
