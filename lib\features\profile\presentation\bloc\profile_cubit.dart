﻿import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/error/failures.dart';
import '../../domain/usecases/get_profile_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';
import '../../../authentication/data/models/deconnexion_request.dart';
import 'profile_state.dart';

/// Profile Cubit for managing profile state
class ProfileCubit extends Cubit<ProfileState> {
  final GetProfileUseCase _getProfileUseCase;
  final LogoutUseCase _logoutUseCase;

  ProfileCubit({
    required GetProfileUseCase getProfileUseCase,
    required LogoutUseCase logoutUseCase,
  }) : _getProfileUseCase = getProfileUseCase,
       _logoutUseCase = logoutUseCase,
       super(const ProfileInitial());
  
  /// Load profile data
  Future<void> loadProfileData() async {
    emit(const ProfileLoading());

    final failureOrProfile = await _getProfileUseCase(NoParams());

    failureOrProfile.fold(
      (failure) => emit(ProfileError(_mapFailureToMessage(failure))),
      (profile) {
        if (profile != null) {
          emit(ProfileLoaded(profile: profile));
        } else {
          emit(const ProfileNotFound());
        }
      },
    );
  }
  
  /// Refresh profile data
  Future<void> refresh() async {
    await loadProfileData();
  }

  /// Logout user
  Future<void> logout(DeconnexionRequest request) async {
    emit(const LogoutLoading());

    final failureOrSuccess = await _logoutUseCase(request);

    failureOrSuccess.fold(
      (failure) => emit(ProfileError(_mapFailureToMessage(failure))),
      (_) => emit(const LogoutSuccess()),
    );
  }

  /// Map failure to user-friendly message
  String _mapFailureToMessage(Failure failure) {
    if (failure is ServerFailure) {
      return failure.message;
    } else if (failure is NetworkFailure) {
      return 'Erreur de connexion. Vérifiez votre connexion internet.';
    } else if (failure is CacheFailure) {
      return 'Erreur de stockage local.';
    } else {
      return 'Une erreur inattendue s\'est produite.';
    }
  }
}
