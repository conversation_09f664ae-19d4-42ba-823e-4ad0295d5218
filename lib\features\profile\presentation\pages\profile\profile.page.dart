import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import 'package:Kairos/core/services/device_info_service.dart';
import 'package:Kairos/features/authentication/data/models/deconnexion_request.dart';
import '../../../domain/entities/profile_entity.dart';
import '../../bloc/profile_cubit.dart';
import '../../bloc/profile_state.dart';


class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  void initState() {
    super.initState();
    // Load profile data when page initializes
    context.read<ProfileCubit>().loadProfileData();
  }

  void _editProfileImage() {
    debugPrint('Edit profile image button tapped');
  }

  void _goToListSchools(){
    debugPrint("User wants to his list of schools");
    Navigator.pushNamed(context, "/liste_etablissement");
  }

  // Function to show the account deletion confirmation alert
  void _showDeleteConfirmationAlert(ProfileEntity profile) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: const Text("CONFIRMATION DE SUPPRESSION", textAlign: TextAlign.center, style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
          insetPadding: const EdgeInsets.symmetric(horizontal: 10),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.warning_amber_outlined,
                color: Colors.red,
                size: 50,
              ),
              const Text(
                "Êtes-vous sûr de vouloir supprimer votre compte ? Cette action est irréversible.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.black,
                ),
              ),
            ],
          ),
          actionsAlignment: MainAxisAlignment.spaceBetween, // Align buttons to start and end
          actionsPadding: const EdgeInsets.all(15.0),
          actions: [
            // Left-aligned "SUPPRIMER" button
            FilledButton(
              style: ButtonStyle(
                foregroundColor: WidgetStateProperty.all(Colors.white),
                minimumSize: WidgetStateProperty.all(const Size(150, 50)),
                backgroundColor: WidgetStateProperty.all(Colors.red),
              ),
              onPressed: () {
                _logout(profile); // Execute the logout function
                Navigator.of(context).pop(); // Dismiss the alert
              },
              child: const Text('SUPPRIMER'),
            ),
            // Right-aligned "ANNULER" button
            FilledButton(
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                minimumSize: WidgetStateProperty.all(const Size(150, 50)), // Ensure consistent size
              ),
              onPressed: () {
                Navigator.of(context).pop(); // Dismiss the alert
              },
              child: const Text('ANNULER'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _logout(ProfileEntity profile) async {
    try {
      // Get device info
      final deviceInfo = DeviceInfoService.deviceInfo;

      // Create deconnexion request
      final request = DeconnexionRequest(
        numeroTelephone: profile.phoneNumber,
        marqueTelephone: deviceInfo.marqueTelephone,
        modelTelephone: deviceInfo.modelTelephone,
        imeiTelephone: deviceInfo.imeiTelephone,
        numeroSerie: deviceInfo.numeroSerie,
        codeUtilisateur: profile.codeUtilisateur,
        codeEtab: profile.schoolCode,
      );

    debugPrint('ProfilePage: Logout request: ${request.toJson()}');
      // Call logout
      context.read<ProfileCubit>().logout(request);
    } catch (e) {
      debugPrint('Error creating logout request: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    const double heroHeight = 200.0;
    const double avatarRadius = 100.0;
    const double avatarTopPosition = (heroHeight - avatarRadius)/2 ;

    return BlocListener<ProfileCubit, ProfileState>(
      listener: (context, state) {
        if (state is LogoutSuccess) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(
              message: "Votre demande de suppression de votre espace a été enregistrée avec succès",
            ).getSnackBar(),
          );
          // Navigate to liste_etablissement page
          Navigator.pushReplacementNamed(context, '/liste_etablissement');
        } else if (state is ProfileError) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            CustomSnackbar(message: state.message).getSnackBar(),
          );
        }
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          title: const Text('Profile'),
          centerTitle: true,
          foregroundColor: Colors.white,
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: heroHeight+avatarRadius/2, // Provide a bounded height
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Hero(
                      tag: "hero_profile",
                      transitionOnUserGestures: true,
                      child: Image.asset("assets/images/header_dashboard.png", width: MediaQuery.of(context).size.width, fit: BoxFit.cover,),
                    ),
                    BlocBuilder<ProfileCubit, ProfileState>(
                      builder: (context, state) {
                        // Show edit button only if profile is loaded
                        final showEditButton = state is ProfileLoaded;

                        return Stack(
                          children: [
                            Positioned(
                              top: avatarTopPosition,
                              left: MediaQuery.of(context).size.width / 2 - avatarRadius,
                              child: BlocBuilder<ProfileCubit, ProfileState>(
                                builder: (context, state) {
                                  ImageProvider backgroundImage;
                                  if (state is ProfileLoaded && state.profile.photo.isNotEmpty) {
                                    debugPrint("ProfilePage: Building profile avatar with photo: ${state.profile.photo}");
                                    try {
                                      backgroundImage = MemoryImage(
                                        // Remove data:image/...;base64, if present
                                        base64Decode(state.profile.photo.replaceFirst(RegExp(r'^data:image\/[^;]+;base64,'), '')),
                                      );
                                    } catch (_) {
                                      backgroundImage = AssetImage('assets/images/default_profile_image.jpg');
                                    }
                                  } else {
                                    debugPrint("ProfilePage: Building profile avatar with default image");
                                    backgroundImage = AssetImage('assets/images/default_profile_image.jpg');
                                  }
                                  return CircleAvatar(
                                    radius: avatarRadius,
                                    backgroundImage: backgroundImage,
                                  );
                                },
                              ),
                            ),
                            if (showEditButton)
                              Positioned(
                                top: avatarTopPosition+30,
                                left: MediaQuery.of(context).size.width / 2 + avatarRadius - 30,
                                child: Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black26,
                                        blurRadius: 4.0,
                                        offset: Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: IconButton(
                                    icon: const Icon(Icons.camera_alt),
                                    color: Colors.blue,
                                    onPressed: _editProfileImage,
                                  ),
                                ),
                              ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 0, left: 16, right: 16),
                child: BlocBuilder<ProfileCubit, ProfileState>(
                  builder: (context, state) {
                    if (state is ProfileLoading || state is LogoutLoading) {
                      return Center(
                        heightFactor: 4,
                        child: CustomSpinner(
                          size: 60.0,
                          strokeWidth: 5.0,
                        ),
                      );
                    } else if (state is ProfileNotFound) {
                      return Center(
                        heightFactor: 2,
                        child: Text(
                          "Vous n'avez pas encore activé votre espace, veuillez activer un établissement afin d'accéder à votre profil",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      );
                    } else if (state is ProfileLoaded) {
                      return _buildProfileContent(state.profile);
                    } else if (state is ProfileError) {
                      return Center(
                        heightFactor: 4,
                        child: Column(
                          children: [
                            Text(
                              'Erreur: ${state.message}',
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.red),
                            ),
                            SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () => context.read<ProfileCubit>().loadProfileData(),
                              child: Text('Réessayer'),
                            ),
                          ],
                        ),
                      );
                    }
                    return SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileContent(ProfileEntity profile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          title: Text('Nom complet', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor)),
          subtitle: Text(profile.fullName, style: TextStyle(fontSize: 14)),
          contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
          dense: true,
        ),
        Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
        ListTile(
          title: Text('Type de profil', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor)),
          subtitle: Text(profile.profil, style: TextStyle(fontSize: 14)),
          contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
          dense: true,
        ),
        Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
        ListTile(
          title: Text('Identifiant', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor)),
          subtitle: Text(profile.codeUtilisateur, style: TextStyle(fontSize: 14)),
          contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
          dense: true,
        ),
        Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
        ListTile(
          title: Text('Établissement', style: TextStyle(fontSize: 8,  color: Theme.of(context).primaryColor)),
          subtitle: Text(profile.schoolName, style: TextStyle(fontSize: 14)),
          contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
          dense: true,
        ),
        Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
        ListTile(
          title: Text('Numéro de téléphone', style: TextStyle(fontSize: 8, color: Theme.of(context).primaryColor),),
          subtitle: Text(profile.phoneNumber, style: TextStyle(fontSize: 14)),
          contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
          dense: true,
        ),
        Divider(height: 2, indent: 12, endIndent: 12, color: Theme.of(context).colorScheme.secondary,),
        const SizedBox(height: 40.0),
        Center(child:
        FilledButton(onPressed: _goToListSchools,
        style: ButtonStyle(
          minimumSize: WidgetStateProperty.all(const Size(200, 50)),
          backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
        ),
        child: const Text("VOIR LISTE ÉTABLISSEMENT")),
        ),
        const SizedBox(height: 7.0),
        Center(
          child: FilledButton(
            onPressed: () => _showDeleteConfirmationAlert(profile), // Call the new alert function
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              minimumSize: const Size(200, 50),
            ),
            child: const Text(
              'DÉCONNEXION',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ),

        const SizedBox(height: 20.0),
      ],
    );
  }
}
