import 'package:equatable/equatable.dart';

/// Entity for user-associated school information
class EtablissementUtilisateur extends Equatable {
  final String libelleEtab;
  final String codeEtab;
  final String logoEtablissement;
  final String profil;
  final String codeUtilisateur;

  const EtablissementUtilisateur({
    required this.libelleEtab,
    required this.codeEtab,
    required this.logoEtablissement,
    required this.profil,
    required this.codeUtilisateur,
  });

  @override
  List<Object?> get props => [
        libelleEtab,
        codeEtab,
        logoEtablissement,
        profil,
        codeUtilisateur,
      ];
}